# 🌊 流式传输使用指南

Figma Context MCP 支持多种流式传输方式，可以提供实时进度更新和更好的用户体验。

## 🚀 快速开始

### 1. 启动流式传输模式

```bash
# 启动 HTTP 服务器（支持流式传输）
npx figma-context-mcp --figma-api-key=YOUR_TOKEN --port=3000

# 自定义端口
npx figma-context-mcp --figma-api-key=YOUR_TOKEN --port=8080
```

### 2. 可用的流式传输端点

启动后，服务器会提供以下端点：

- **StreamableHTTP**: `http://localhost:3000/mcp`
- **SSE (Server-Sent Events)**: `http://localhost:3000/sse`  
- **Messages**: `http://localhost:3000/messages`

## 📊 进度通知功能

### 使用进度通知

在调用工具时添加 `progressToken` 参数：

```javascript
{
  "method": "tools/call",
  "params": {
    "name": "get_figma_data",
    "arguments": {
      "fileKey": "your-figma-file-key"
    },
    "_meta": {
      "progressToken": "unique-progress-id-123"
    }
  }
}
```

### 接收进度更新

通过 SSE 连接接收进度通知：

```javascript
// 进度通知格式
{
  "method": "notifications/progress",
  "params": {
    "progressToken": "unique-progress-id-123",
    "progress": 50,    // 当前进度
    "total": 100       // 总进度
  }
}
```

## 🔧 可用的流式传输工具

### 1. `get_figma_data` - 带进度的标准获取

```javascript
{
  "name": "get_figma_data",
  "arguments": {
    "fileKey": "ABC123",
    "nodeId": "optional-node-id",
    "depth": 2
  },
  "_meta": {
    "progressToken": "progress-token-1"
  }
}
```

**进度阶段：**
- 0% - 开始获取
- 50% - 数据获取完成
- 80% - 数据处理完成  
- 100% - 格式化完成

### 2. `get_figma_data_stream` - 分块流式处理

```javascript
{
  "name": "get_figma_data_stream", 
  "arguments": {
    "fileKey": "ABC123",
    "chunkSize": 25,     // 每块处理的节点数
    "depth": 2
  },
  "_meta": {
    "progressToken": "stream-progress-2"
  }
}
```

**特点：**
- 大文件分块处理
- 实时进度更新
- 内存使用优化
- 支持 YAML 和 JSON 格式

**进度阶段：**
- 0-20% - 元数据和全局变量
- 20-100% - 节点分块处理

### 3. `download_figma_images` - 流式图片下载

```javascript
{
  "name": "download_figma_images",
  "arguments": {
    "fileKey": "ABC123",
    "nodes": [
      {"nodeId": "1:2", "fileName": "icon.svg"},
      {"nodeId": "1:3", "fileName": "image.png"}
    ],
    "localPath": "/path/to/images"
  },
  "_meta": {
    "progressToken": "download-progress-3"
  }
}
```

**进度跟踪：**
- 每个图片下载完成后更新进度
- `progress` = 已下载数量
- `total` = 总图片数量

## 🛠️ 客户端集成示例

### Node.js 客户端

```javascript
import fetch from 'node-fetch';
import { EventSource } from 'eventsource';

class MCPStreamingClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.sessionId = null;
  }

  async initialize() {
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: { tools: {} },
          clientInfo: { name: 'my-client', version: '1.0.0' }
        }
      })
    });

    this.sessionId = response.headers.get('mcp-session-id');
    return response.json();
  }

  async callToolWithProgress(toolName, args) {
    const progressToken = `progress-${Date.now()}`;
    
    // 监听进度
    this.listenForProgress(progressToken);
    
    // 调用工具
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': this.sessionId
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args,
          _meta: { progressToken }
        }
      })
    });

    return response.json();
  }

  listenForProgress(progressToken) {
    const eventSource = new EventSource(`${this.baseUrl}/sse`, {
      headers: { 'mcp-session-id': this.sessionId }
    });

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.method === 'notifications/progress' && 
          data.params?.progressToken === progressToken) {
        const { progress, total } = data.params;
        console.log(`Progress: ${progress}/${total}`);
      }
    };
  }
}
```

### 使用示例

```javascript
const client = new MCPStreamingClient('http://localhost:3000');
await client.initialize();

// 带进度的标准获取
await client.callToolWithProgress('get_figma_data', {
  fileKey: 'your-figma-file-key'
});

// 流式分块处理
await client.callToolWithProgress('get_figma_data_stream', {
  fileKey: 'your-figma-file-key',
  chunkSize: 20
});
```

## ⚙️ 配置选项

### 环境变量

```bash
# API 认证
FIGMA_API_KEY=your_api_key
FIGMA_OAUTH_TOKEN=your_oauth_token

# 服务器配置
PORT=3000
OUTPUT_FORMAT=yaml  # 或 json

# 日志级别
NODE_ENV=development
```

### 命令行参数

```bash
npx figma-context-mcp \
  --figma-api-key=YOUR_TOKEN \
  --port=3000 \
  --json \
  --env=/path/to/.env
```

## 🔍 调试和监控

### 启用详细日志

```bash
NODE_ENV=development npx figma-context-mcp --figma-api-key=YOUR_TOKEN --port=3000
```

### 监控端点

- **健康检查**: `GET http://localhost:3000/health`
- **服务器状态**: 查看控制台输出
- **活跃连接**: 服务器会记录 SSE 和 StreamableHTTP 连接

## 🚨 注意事项

1. **端口冲突**: 确保指定的端口未被占用
2. **防火墙**: 确保端口对客户端开放
3. **内存使用**: 大文件建议使用 `get_figma_data_stream` 工具
4. **网络稳定性**: SSE 连接需要稳定的网络环境
5. **会话管理**: HTTP 模式下会话会自动管理，无需手动清理

## 📝 完整示例

查看 `examples/streaming-example.js` 获取完整的工作示例。

```bash
# 1. 启动服务器
npx figma-context-mcp --figma-api-key=YOUR_TOKEN --port=3000

# 2. 在另一个终端运行示例
node examples/streaming-example.js
```
