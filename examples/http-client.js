#!/usr/bin/env node

/**
 * HTTP 模式客户端示例
 * 
 * 使用方法：
 * 1. 启动服务器: npx figma-context-mcp --figma-api-key=YOUR_KEY --port=1932
 * 2. 运行客户端: node examples/http-client.js
 */

import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:1932';
const FIGMA_FILE_KEY = 'your-figma-file-key-here'; // 替换为实际的 Figma 文件 key

class FigmaMCPClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.sessionId = null;
  }

  async initialize() {
    console.log('🚀 初始化 MCP 连接...');
    
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {},
          },
          clientInfo: {
            name: 'figma-http-client',
            version: '1.0.0',
          },
        },
      }),
    });

    const result = await response.json();
    this.sessionId = response.headers.get('mcp-session-id');
    
    console.log('✅ MCP 连接已初始化');
    console.log('📋 会话 ID:', this.sessionId);
    console.log('🛠️  服务器能力:', JSON.stringify(result.result?.capabilities, null, 2));
    
    return result;
  }

  async listTools() {
    console.log('\n📋 获取可用工具列表...');
    
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': this.sessionId,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/list',
      }),
    });

    const result = await response.json();
    console.log('🔧 可用工具:');
    result.result?.tools?.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    
    return result;
  }

  async callTool(toolName, args, progressToken = null) {
    console.log(`\n🔧 调用工具: ${toolName}`);
    console.log('📝 参数:', JSON.stringify(args, null, 2));
    
    const params = {
      name: toolName,
      arguments: args,
    };
    
    if (progressToken) {
      params._meta = { progressToken };
      console.log('📊 进度令牌:', progressToken);
    }

    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': this.sessionId,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params,
      }),
    });

    const result = await response.json();
    
    if (result.error) {
      console.error('❌ 工具调用失败:', result.error);
      return result;
    }
    
    console.log('✅ 工具调用成功');
    console.log('📄 结果预览:', result.result?.content?.[0]?.text?.substring(0, 200) + '...');
    
    return result;
  }

  async demonstrateUsage() {
    try {
      // 1. 初始化连接
      await this.initialize();
      
      // 2. 列出可用工具
      await this.listTools();
      
      // 3. 调用基本工具
      console.log('\n' + '='.repeat(50));
      console.log('🎯 演示 1: 获取 Figma 文件数据');
      console.log('='.repeat(50));
      
      await this.callTool('get_figma_data', {
        fileKey: FIGMA_FILE_KEY,
      });
      
      // 4. 调用流式工具
      console.log('\n' + '='.repeat(50));
      console.log('🎯 演示 2: 流式获取数据（分块处理）');
      console.log('='.repeat(50));
      
      await this.callTool('get_figma_data_stream', {
        fileKey: FIGMA_FILE_KEY,
        chunkSize: 10,
      }, `progress-${Date.now()}`);
      
    } catch (error) {
      console.error('❌ 演示失败:', error);
    }
  }
}

// 运行演示
async function main() {
  console.log('🌟 Figma MCP HTTP 客户端演示');
  console.log('================================\n');

  if (FIGMA_FILE_KEY === 'your-figma-file-key-here') {
    console.log('⚠️  请在脚本中更新 FIGMA_FILE_KEY');
    console.log('   示例: https://www.figma.com/file/ABC123/my-design -> 文件 key 是 "ABC123"');
    return;
  }

  const client = new FigmaMCPClient(SERVER_URL);
  await client.demonstrateUsage();
}

main().catch(console.error);
