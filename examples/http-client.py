#!/usr/bin/env python3

"""
HTTP 模式 Python 客户端示例

使用方法：
1. 安装依赖: pip install requests
2. 启动服务器: npx figma-context-mcp --figma-api-key=YOUR_KEY --port=1932
3. 运行客户端: python examples/http-client.py
"""

import requests
import json
import time

SERVER_URL = 'http://localhost:1932'
FIGMA_FILE_KEY = 'your-figma-file-key-here'  # 替换为实际的 Figma 文件 key

class FigmaMCPClient:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session_id = None
        self.session = requests.Session()

    def initialize(self):
        print('🚀 初始化 MCP 连接...')
        
        payload = {
            'jsonrpc': '2.0',
            'id': 1,
            'method': 'initialize',
            'params': {
                'protocolVersion': '2024-11-05',
                'capabilities': {'tools': {}},
                'clientInfo': {'name': 'figma-python-client', 'version': '1.0.0'}
            }
        }
        
        response = self.session.post(
            f'{self.base_url}/mcp',
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        result = response.json()
        self.session_id = response.headers.get('mcp-session-id')
        
        print('✅ MCP 连接已初始化')
        print(f'📋 会话 ID: {self.session_id}')
        print(f'🛠️  服务器能力: {json.dumps(result.get("result", {}).get("capabilities", {}), indent=2)}')
        
        return result

    def list_tools(self):
        print('\n📋 获取可用工具列表...')
        
        payload = {
            'jsonrpc': '2.0',
            'id': 2,
            'method': 'tools/list'
        }
        
        response = self.session.post(
            f'{self.base_url}/mcp',
            json=payload,
            headers={
                'Content-Type': 'application/json',
                'mcp-session-id': self.session_id
            }
        )
        
        result = response.json()
        tools = result.get('result', {}).get('tools', [])
        
        print('🔧 可用工具:')
        for tool in tools:
            print(f'  - {tool["name"]}: {tool["description"]}')
        
        return result

    def call_tool(self, tool_name, args, progress_token=None):
        print(f'\n🔧 调用工具: {tool_name}')
        print(f'📝 参数: {json.dumps(args, indent=2)}')
        
        params = {
            'name': tool_name,
            'arguments': args
        }
        
        if progress_token:
            params['_meta'] = {'progressToken': progress_token}
            print(f'📊 进度令牌: {progress_token}')
        
        payload = {
            'jsonrpc': '2.0',
            'id': 3,
            'method': 'tools/call',
            'params': params
        }
        
        response = self.session.post(
            f'{self.base_url}/mcp',
            json=payload,
            headers={
                'Content-Type': 'application/json',
                'mcp-session-id': self.session_id
            }
        )
        
        result = response.json()
        
        if 'error' in result:
            print(f'❌ 工具调用失败: {result["error"]}')
            return result
        
        print('✅ 工具调用成功')
        content = result.get('result', {}).get('content', [])
        if content and content[0].get('text'):
            preview = content[0]['text'][:200] + '...'
            print(f'📄 结果预览: {preview}')
        
        return result

    def demonstrate_usage(self):
        try:
            # 1. 初始化连接
            self.initialize()
            
            # 2. 列出可用工具
            self.list_tools()
            
            # 3. 调用基本工具
            print('\n' + '='*50)
            print('🎯 演示 1: 获取 Figma 文件数据')
            print('='*50)
            
            self.call_tool('get_figma_data', {
                'fileKey': FIGMA_FILE_KEY
            })
            
            # 4. 调用流式工具
            print('\n' + '='*50)
            print('🎯 演示 2: 流式获取数据（分块处理）')
            print('='*50)
            
            self.call_tool('get_figma_data_stream', {
                'fileKey': FIGMA_FILE_KEY,
                'chunkSize': 10
            }, f'progress-{int(time.time())}')
            
        except Exception as error:
            print(f'❌ 演示失败: {error}')

def main():
    print('🌟 Figma MCP HTTP Python 客户端演示')
    print('===================================\n')

    if FIGMA_FILE_KEY == 'your-figma-file-key-here':
        print('⚠️  请在脚本中更新 FIGMA_FILE_KEY')
        print('   示例: https://www.figma.com/file/ABC123/my-design -> 文件 key 是 "ABC123"')
        return

    client = FigmaMCPClient(SERVER_URL)
    client.demonstrate_usage()

if __name__ == '__main__':
    main()
