<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Figma MCP HTTP 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, textarea, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007AFF;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056CC;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007AFF;
        }
        .error {
            border-left-color: #FF3B30;
            background-color: #fff5f5;
        }
        .log {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Figma MCP HTTP 测试工具</h1>
        
        <div class="input-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:1932" placeholder="http://localhost:1932">
        </div>
        
        <div class="input-group">
            <label for="figmaFileKey">Figma 文件 Key:</label>
            <input type="text" id="figmaFileKey" placeholder="从 Figma URL 中获取文件 key">
            <small>示例: https://www.figma.com/file/ABC123/my-design → 文件 key 是 "ABC123"</small>
        </div>
        
        <button onclick="initializeConnection()">1. 初始化连接</button>
        <button onclick="listTools()" disabled id="listToolsBtn">2. 获取工具列表</button>
        <button onclick="getFigmaData()" disabled id="getFigmaDataBtn">3. 获取 Figma 数据</button>
        <button onclick="getStreamData()" disabled id="getStreamDataBtn">4. 流式获取数据</button>
        
        <div id="status"></div>
        <div id="result"></div>
    </div>

    <script>
        let sessionId = null;
        let serverUrl = '';

        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML = `<div class="status ${type}">[${timestamp}] ${message}</div>` + statusDiv.innerHTML;
        }

        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="result ${isError ? 'error' : ''}">
                    <h3>${isError ? '❌ 错误' : '✅ 结果'}</h3>
                    <div class="log">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;
        }

        async function makeRequest(method, params = {}) {
            const payload = {
                jsonrpc: '2.0',
                id: Date.now(),
                method: method,
                params: params
            };

            const headers = {
                'Content-Type': 'application/json'
            };

            if (sessionId) {
                headers['mcp-session-id'] = sessionId;
            }

            try {
                const response = await fetch(`${serverUrl}/mcp`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(payload)
                });

                if (!sessionId && response.headers.get('mcp-session-id')) {
                    sessionId = response.headers.get('mcp-session-id');
                    log(`获取到会话 ID: ${sessionId}`, 'success');
                }

                const result = await response.json();
                return result;
            } catch (error) {
                throw new Error(`网络请求失败: ${error.message}`);
            }
        }

        async function initializeConnection() {
            try {
                serverUrl = document.getElementById('serverUrl').value;
                log('正在初始化 MCP 连接...', 'info');

                const result = await makeRequest('initialize', {
                    protocolVersion: '2024-11-05',
                    capabilities: { tools: {} },
                    clientInfo: { name: 'figma-web-client', version: '1.0.0' }
                });

                if (result.error) {
                    throw new Error(result.error.message);
                }

                log('MCP 连接初始化成功!', 'success');
                showResult(result);

                // 启用其他按钮
                document.getElementById('listToolsBtn').disabled = false;
                document.getElementById('getFigmaDataBtn').disabled = false;
                document.getElementById('getStreamDataBtn').disabled = false;

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                showResult({ error: error.message }, true);
            }
        }

        async function listTools() {
            try {
                log('正在获取工具列表...', 'info');
                const result = await makeRequest('tools/list');

                if (result.error) {
                    throw new Error(result.error.message);
                }

                log(`找到 ${result.result.tools.length} 个工具`, 'success');
                showResult(result);

            } catch (error) {
                log(`获取工具列表失败: ${error.message}`, 'error');
                showResult({ error: error.message }, true);
            }
        }

        async function getFigmaData() {
            try {
                const fileKey = document.getElementById('figmaFileKey').value;
                if (!fileKey) {
                    throw new Error('请输入 Figma 文件 Key');
                }

                log('正在获取 Figma 数据...', 'info');
                const result = await makeRequest('tools/call', {
                    name: 'get_figma_data',
                    arguments: { fileKey: fileKey }
                });

                if (result.error) {
                    throw new Error(result.error.message);
                }

                log('Figma 数据获取成功!', 'success');
                showResult(result);

            } catch (error) {
                log(`获取 Figma 数据失败: ${error.message}`, 'error');
                showResult({ error: error.message }, true);
            }
        }

        async function getStreamData() {
            try {
                const fileKey = document.getElementById('figmaFileKey').value;
                if (!fileKey) {
                    throw new Error('请输入 Figma 文件 Key');
                }

                log('正在流式获取 Figma 数据...', 'info');
                const result = await makeRequest('tools/call', {
                    name: 'get_figma_data_stream',
                    arguments: { 
                        fileKey: fileKey,
                        chunkSize: 10
                    },
                    _meta: {
                        progressToken: `web-progress-${Date.now()}`
                    }
                });

                if (result.error) {
                    throw new Error(result.error.message);
                }

                log('流式数据获取成功!', 'success');
                showResult(result);

            } catch (error) {
                log(`流式获取数据失败: ${error.message}`, 'error');
                showResult({ error: error.message }, true);
            }
        }
    </script>
</body>
</html>
