#!/usr/bin/env node

/**
 * Example of using Figma Context MCP with streaming support
 * 
 * Usage:
 * 1. Start the MCP server: npx figma-context-mcp --figma-api-key=YOUR_TOKEN --port=3000
 * 2. Run this script: node examples/streaming-example.js
 */

import fetch from 'node-fetch';
import { EventSource } from 'eventsource';

const MCP_SERVER_URL = 'http://localhost:3000';
const FIGMA_FILE_KEY = 'your-figma-file-key-here'; // Replace with actual file key

class MCPStreamingClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.sessionId = null;
  }

  async initialize() {
    console.log('🚀 Initializing MCP connection...');
    
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {},
          },
          clientInfo: {
            name: 'streaming-example',
            version: '1.0.0',
          },
        },
      }),
    });

    const result = await response.json();
    this.sessionId = response.headers.get('mcp-session-id');
    
    console.log('✅ MCP connection initialized');
    console.log('📋 Session ID:', this.sessionId);
    console.log('🛠️  Available capabilities:', result.result?.capabilities);
    
    return result;
  }

  async callToolWithProgress(toolName, args) {
    if (!this.sessionId) {
      throw new Error('MCP not initialized. Call initialize() first.');
    }

    const progressToken = `progress-${Date.now()}`;
    console.log(`\n🔧 Calling tool: ${toolName}`);
    console.log('📊 Progress token:', progressToken);

    // Set up SSE connection for progress notifications
    const progressPromise = this.listenForProgress(progressToken);

    // Make the tool call
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': this.sessionId,
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args,
          _meta: {
            progressToken,
          },
        },
      }),
    });

    const result = await response.json();
    
    // Wait a bit for final progress updates
    setTimeout(() => {
      console.log('✅ Tool call completed');
    }, 1000);

    return result;
  }

  async listenForProgress(progressToken) {
    return new Promise((resolve) => {
      console.log('👂 Listening for progress updates...');
      
      const eventSource = new EventSource(`${this.baseUrl}/sse`, {
        headers: {
          'mcp-session-id': this.sessionId,
        },
      });

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.method === 'notifications/progress' && 
              data.params?.progressToken === progressToken) {
            const { progress, total } = data.params;
            const percentage = total ? Math.round((progress / total) * 100) : progress;
            
            console.log(`📈 Progress: ${progress}/${total} (${percentage}%)`);
            
            if (progress >= total) {
              eventSource.close();
              resolve();
            }
          }
        } catch (error) {
          console.error('❌ Error parsing progress data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('❌ SSE connection error:', error);
        eventSource.close();
        resolve();
      };

      // Auto-close after 30 seconds
      setTimeout(() => {
        eventSource.close();
        resolve();
      }, 30000);
    });
  }

  async demonstrateStreaming() {
    try {
      // Initialize connection
      await this.initialize();

      console.log('\n' + '='.repeat(50));
      console.log('🎯 Demo 1: Regular tool with progress tracking');
      console.log('='.repeat(50));

      const result1 = await this.callToolWithProgress('get_figma_data', {
        fileKey: FIGMA_FILE_KEY,
      });

      console.log('📄 Result preview:', result1.result?.content?.[0]?.text?.substring(0, 200) + '...');

      console.log('\n' + '='.repeat(50));
      console.log('🎯 Demo 2: Streaming tool with chunked processing');
      console.log('='.repeat(50));

      const result2 = await this.callToolWithProgress('get_figma_data_stream', {
        fileKey: FIGMA_FILE_KEY,
        chunkSize: 10,
      });

      console.log('📄 Streaming result preview:', result2.result?.content?.[0]?.text?.substring(0, 200) + '...');

    } catch (error) {
      console.error('❌ Demo failed:', error);
    }
  }
}

// Run the demo
async function main() {
  console.log('🌟 Figma Context MCP Streaming Demo');
  console.log('=====================================\n');

  if (FIGMA_FILE_KEY === 'your-figma-file-key-here') {
    console.log('⚠️  Please update FIGMA_FILE_KEY in this script with a real Figma file key');
    console.log('   Example: https://www.figma.com/file/ABC123/my-design -> file key is "ABC123"');
    return;
  }

  const client = new MCPStreamingClient(MCP_SERVER_URL);
  await client.demonstrateStreaming();
}

main().catch(console.error);
